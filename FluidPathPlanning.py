"""
基于流体动力学的路径规划算法
===========================================

本模块实现了基于格子玻尔兹曼方法(LBM)的流体模拟和路径规划算法。
通过模拟障碍物周围的流体流动，提取流体场信息来指导路径规划。

主要功能:
1. 使用LBM进行流体模拟
2. 基于流线的路径规划
3. 结合流体场信息的增强A*算法
4. 路径性能分析与可视化

作者: [Your Name]
日期: 2025-05-16
"""

# 导入必要的库
import numpy as np                                  # 用于数值计算和数组操作
import matplotlib.pyplot as plt                     # 用于数据可视化
from matplotlib.colors import LinearSegmentedColormap  # 用于创建自定义颜色映射
import time                                         # 用于计时和性能测量
import multiprocessing as mp                        # 用于并行计算加速
from functools import partial                       # 用于创建偏函数，简化并行处理
import heapq                                        # 用于实现优先队列，A*算法中使用


# 设置全局绘图参数，使用Times New Roman字体
plt.rcParams['font.family'] = 'Times New Roman'     # 设置全局字体为Times New Roman
plt.rcParams['font.size'] = 12                      # 设置基本字体大小
plt.rcParams['axes.titlesize'] = 14                 # 设置标题字体大小
plt.rcParams['axes.labelsize'] = 12                 # 设置坐标轴标签字体大小
plt.rcParams['xtick.labelsize'] = 10                # 设置x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 10                # 设置y轴刻度标签字体大小

#################################################
# 第一部分：LBM流体模拟 (LBM Fluid Simulation)
# 描述：包含所有格子玻尔兹曼方法相关的函数和常量，
#      用于模拟流体动力学。
#################################################

# D2Q9模型的速度集（格子玻尔兹曼方法中的9个离散速度方向）
# 这些向量定义了粒子在格子上可能的运动方向
c = np.array([
    [0, 0],    # 0: 静止不动的粒子
    [1, 0],    # 1: 向东移动（右）
    [0, 1],    # 2: 向北移动（上）
    [-1, 0],   # 3: 向西移动（左）
    [0, -1],   # 4: 向南移动（下）
    [1, 1],    # 5: 向东北移动（右上）
    [-1, 1],   # 6: 向西北移动（左上）
    [-1, -1],  # 7: 向西南移动（左下）
    [1, -1]    # 8: 向东南移动（右下）
])

# D2Q9模型的权重系数（用于计算平衡分布函数）
# 这些权重反映了各个方向上粒子分布的概率
# 静止粒子权重最大(4/9)，正交方向次之(1/9)，对角方向最小(1/36)
w = np.array([4/9, 1/9, 1/9, 1/9, 1/9, 1/36, 1/36, 1/36, 1/36])

# 反向索引，用于反弹边界条件（每个方向的反方向索引）
# 例如：方向1(东)的反方向是3(西)，方向5(东北)的反方向是7(西南)
opposite = np.array([0, 3, 4, 1, 2, 7, 8, 5, 6])

def add_boundary(grid_map):
    """
    在地图四周添加障碍物边界，并在右下角和左上角创建入口和出口区域

    参数:
        grid_map: 原始网格地图，0表示流体，1表示障碍物

    返回:
        添加了边界、入口和出口的新地图

    说明:
        - 根据地图尺寸动态调整入口和出口区域的大小
        - 入口位于右下角，出口位于左上角
        - 入口和出口区域呈对角线形状，确保与真实地图有足够的接触面积
    """
    h, w = grid_map.shape  # 获取原始地图的高度和宽度

    # 计算扩展区域的大小（取网格尺寸的10%和1的较大值）
    # 这样可以确保扩展区域与原始地图尺寸成比例，但不会太小
    extension_size = min(int(min(h, w) * 0.1), 1)

    # 创建新的扩展地图，包括原始区域、扩展区域和边界
    # 额外的2是为了边界，2*extension_size是为了入口和出口
    new_h = h + 2 + 2 * extension_size
    new_w = w + 2 + 2 * extension_size
    new_map = np.ones((new_h, new_w))  # 初始化为障碍物（1）

    # 将原始地图复制到新地图的中心区域
    # 保留原始地图的所有特征，周围添加边界
    new_map[extension_size+1:-extension_size-1, extension_size+1:-extension_size-1] = grid_map

    # 创建右下角入口扩展区域（对角线形式）
    # 这个区域将作为流体的入口，模拟流体从右下角流入
    for i in range(new_h - 2 * extension_size - 1, new_h):
        for j in range(new_w - 2 * extension_size - 1, new_w):
            # 创建对角线形式的入口，只有在对角线附近的点才设为流体
            # 使用相对坐标计算对角线位置，使入口呈现一个斜向通道
            rel_i = i - (new_h - 2 * extension_size - 1)
            rel_j = j - (new_w - 2 * extension_size - 1)
            # 对角线上的点以及附近一定范围内的点设为流体
            if rel_i == rel_j or abs(rel_i - rel_j) <= extension_size // 2:
                new_map[i, j] = 0  # 设为流体区域

    # 创建左上角出口扩展区域（对角线形式）
    # 这个区域将作为流体的出口，模拟流体从左上角流出
    for i in range(2 * extension_size + 1):
        for j in range(2 * extension_size + 1):
            # 创建对角线形式的出口，只有在对角线附近的点才设为流体
            # 与入口类似，创建一个斜向通道作为出口
            if i == j or abs(i - j) <= extension_size // 2:
                new_map[i, j] = 0  # 设为流体区域

    return new_map

def equilibrium(rho, u, v):
    """
    计算平衡分布函数（向量化版本，增强数值稳定性）

    参数:
        rho: 密度场（一维数组）
        u, v: 速度场分量（一维数组）

    返回:
        平衡分布函数数组，形状为(9, len(rho))

    说明:
        - 使用D2Q9模型计算平衡分布函数
        - 限制速度大小以保持数值稳定性
        - 确保平衡分布函数非负，提高数值稳定性
    """
    # 预计算速度平方和，用于后续计算
    usqr = u**2 + v**2

    # 限制速度大小以保持数值稳定性
    # 在LBM中，速度不能太大，否则会导致数值不稳定
    speed_limit = 0.3  # 最大允许速度（格子单位），通常保持在0.1-0.3之间
    speed_magnitude = np.sqrt(usqr)  # 计算速度大小
    speed_mask = speed_magnitude > speed_limit  # 找出超过限制的速度点

    # 如果有速度超过限制，进行缩放处理
    if np.any(speed_mask):
        # 创建副本以避免修改原始数据
        u = u.copy()
        v = v.copy()

        # 确保除数不为零（避免除以零错误）
        # 只对速度非零且超过限制的点进行缩放
        non_zero_speed_mask = speed_mask & (speed_magnitude > 1e-9)  # 添加小的阈值

        # 按比例缩放速度，保持方向不变，但限制大小
        u[non_zero_speed_mask] = u[non_zero_speed_mask] * speed_limit / speed_magnitude[non_zero_speed_mask]
        v[non_zero_speed_mask] = v[non_zero_speed_mask] * speed_limit / speed_magnitude[non_zero_speed_mask]

        # 更新速度平方和
        usqr = u**2 + v**2

    # 初始化平衡分布函数数组
    feq = np.zeros((9, len(rho)))

    # 向量化计算所有方向的平衡分布
    for i in range(9):
        # 计算速度点积 c_i·u（离散速度与宏观速度的点积）
        cu = c[i,0]*u + c[i,1]*v

        # 平衡分布函数公式: f^eq = w_i * rho * (1 + 3(c_i·u) + 4.5(c_i·u)^2 - 1.5u^2)
        # 这是从Maxwell-Boltzmann分布推导出的二阶近似
        feq_temp = w[i]*rho*(1 + 3*cu + 4.5*cu**2 - 1.5*usqr)

        # 确保平衡分布函数非负（物理上分布函数应该是非负的）
        # 添加一个小的正数阈值，避免精度问题导致的负值
        feq[i] = np.maximum(feq_temp, 1e-12)

    return feq

def bounce_back_optimized(f, grid_map):
    """
    处理障碍物边界的反弹边界条件（优化版本）

    参数:
        f: 分布函数数组，形状为(9, nx, ny)
        grid_map: 网格地图，0表示流体，1表示障碍物

    返回:
        应用了反弹边界条件后的分布函数数组

    说明:
        - 使用预计算的反向索引和布尔掩码进行向量化操作
        - 在障碍物位置，将各个方向的分布函数与其反方向交换
        - 跳过静止方向(i=0)，因为它不需要反弹
    """
    # 创建障碍物掩码，标识所有障碍物位置
    # 这个掩码是一个布尔数组，障碍物位置为True，流体位置为False
    obstacle_mask = (grid_map == 1)

    # 对每个方向应用反弹规则
    # 反弹边界条件是LBM中处理固体边界的一种方法
    # 当粒子碰到障碍物时，它会沿着来时的方向反弹回去
    for i in range(1, 9):  # 跳过静止方向(i=0)，因为静止粒子不需要反弹
        # 获取当前方向的反方向索引
        # 例如：向东(i=1)的反方向是向西(i=3)
        opp_i = opposite[i]

        # 在障碍物位置交换分布函数
        # 这模拟了粒子碰到障碍物后反弹的过程
        # 首先保存当前方向的分布函数
        temp = f[i].copy()

        # 将当前方向的分布函数设置为反方向的值（在障碍物位置）
        f[i, obstacle_mask] = f[opp_i, obstacle_mask]

        # 将反方向的分布函数设置为之前保存的当前方向的值（在障碍物位置）
        f[opp_i, obstacle_mask] = temp[obstacle_mask]

    return f

def apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity):
    """
    应用Zou-He边界条件，比简单的速度设置更准确

    参数:
        f: 分布函数数组，形状为(9, nx, ny)
        rho, u, v: 宏观量（密度和速度场）
        grid_map: 网格地图，0表示流体，1表示障碍物
        inlet_velocity: 入口速度大小

    返回:
        更新后的分布函数和宏观量(f, rho, u, v)

    说明:
        - Zou-He边界条件是一种更精确的边界处理方法
        - 在入口处设置速度边界条件（对角线方向流入）
        - 在出口处设置压力边界条件（固定密度）
        - 通过求解宏观量与分布函数之间的关系方程来实现
    """
    nx, ny = grid_map.shape

    # 计算扩展区域的大小，与add_boundary函数保持一致
    # h_orig, w_orig ares original map dimensions before add_boundary extensions
    # We need to refer to the current grid_map's shape (nx, ny) to find extension_size
    # However, extension_size logic in add_boundary was based on the *original* grid_map.
    # For consistency, if we assume add_boundary was called with a map of h_orig x w_orig,
    # leading to nx = h_orig + 2 + 2*ext, ny = w_orig + 2 + 2*ext.
    # We estimate original dimensions to recalculate extension_size.
    # This is a bit circular. A better way would be to pass extension_size or determine it more robustly.
    # For now, let's use the same logic based on current (extended) map, which might not be exactly what add_boundary used.
    # A simpler approach is to identify boundary regions directly.

    # Simplified boundary region identification
    # Inlet: bottom and right edges within the fluid area
    # Outlet: top and left edges within the fluid area

    # 入口边界条件（右下角区域）- 速度边界
    # 流体从右下角流向左上角，所以速度应该是负x方向和正y方向
    u_inlet = -inlet_velocity / np.sqrt(2) #流入负x方向（向左）
    v_inlet = inlet_velocity / np.sqrt(2) #流入正y方向（向上）

    # 右边界入口 (x = nx-1, excluding corners unless they are the only fluid points)
    for i in range(nx):
        if grid_map[i, ny-1] == 0: # Fluid point on right boundary
            u[i,ny-1] = u_inlet
            v[i,ny-1] = v_inlet
            rho_val = (f[0,i,ny-1] + f[2,i,ny-1] + f[4,i,ny-1] + 2*(f[1,i,ny-1] + f[5,i,ny-1] + f[8,i,ny-1])) / (1.0 - u[i,ny-1])
            rho[i,ny-1] = rho_val
            f[3,i,ny-1] = f[1,i,ny-1] + (2./3.) * rho[i,ny-1] * u[i,ny-1]
            f[6,i,ny-1] = f[8,i,ny-1] - 0.5*(f[2,i,ny-1]-f[4,i,ny-1]) + (1./6.)*rho[i,ny-1]*u[i,ny-1] + 0.5*rho[i,ny-1]*v[i,ny-1]
            f[7,i,ny-1] = f[5,i,ny-1] + 0.5*(f[2,i,ny-1]-f[4,i,ny-1]) + (1./6.)*rho[i,ny-1]*u[i,ny-1] - 0.5*rho[i,ny-1]*v[i,ny-1]

    # 下边界入口 (y = ny-1, excluding corners already handled by right boundary if applicable)
    for j in range(ny -1): # Iterate along bottom edge, excluding the very last point (corner)
        if grid_map[nx-1, j] == 0: # Fluid point on bottom boundary
            u[nx-1,j] = u_inlet
            v[nx-1,j] = v_inlet
            rho_val = (f[0,nx-1,j] + f[1,nx-1,j] + f[3,nx-1,j] + 2*(f[2,nx-1,j] + f[5,nx-1,j] + f[6,nx-1,j])) / (1.0 - v[nx-1,j])
            rho[nx-1,j] = rho_val
            f[4,nx-1,j] = f[2,nx-1,j] + (2./3.) * rho[nx-1,j] * v[nx-1,j]
            f[7,nx-1,j] = f[5,nx-1,j] - 0.5*(f[1,nx-1,j]-f[3,nx-1,j]) + 0.5*rho[nx-1,j]*u[nx-1,j] + (1./6.)*rho[nx-1,j]*v[nx-1,j]
            f[8,nx-1,j] = f[6,nx-1,j] + 0.5*(f[1,nx-1,j]-f[3,nx-1,j]) - 0.5*rho[nx-1,j]*u[nx-1,j] + (1./6.)*rho[nx-1,j]*v[nx-1,j]


    # 出口边界条件（左上角区域）- 压力边界
    rho_outlet = 1.0

    # 左边界出口 (x = 0)
    for i in range(nx):
        if grid_map[i, 0] == 0: # Fluid point on left boundary
            rho[i,0] = rho_outlet
            u[i,0] = (f[0,i,0] + f[2,i,0] + f[4,i,0] + 2*(f[3,i,0] + f[6,i,0] + f[7,i,0])) / rho[i,0] - 1.0
            u[i,0] = -1 + (f[0,i,0] + f[2,i,0] + f[4,i,0] + 2*(f[1,i,0] + f[5,i,0] + f[8,i,0])) / rho[i,0] # Corrected formula for u_outlet
            v[i,0] = 0 # Assuming v=0 for this specific Zou-He condition on this edge
            f[1,i,0] = f[3,i,0] + (2./3.) * rho[i,0] * u[i,0]
            f[5,i,0] = f[7,i,0] - 0.5*(f[2,i,0]-f[4,i,0]) + (1./6.)*rho[i,0]*u[i,0] + 0.5*rho[i,0]*v[i,0]
            f[8,i,0] = f[6,i,0] + 0.5*(f[2,i,0]-f[4,i,0]) + (1./6.)*rho[i,0]*u[i,0] - 0.5*rho[i,0]*v[i,0]

    # 上边界出口 (y = 0)
    for j in range(ny -1): # Iterate along top edge, excluding the very last point (corner)
        if grid_map[0, j] == 0: # Fluid point on top boundary
            rho[0,j] = rho_outlet
            v[0,j] = (f[0,0,j] + f[1,0,j] + f[3,0,j] + 2*(f[2,0,j] + f[5,0,j] + f[6,0,j])) / rho[0,j] - 1.0
            v[0,j] = -1 + (f[0,0,j] + f[1,0,j] + f[3,0,j] + 2*(f[4,0,j] + f[7,0,j] + f[8,0,j])) / rho[0,j] # Corrected formula for v_outlet
            u[0,j] = 0 # Assuming u=0 for this specific Zou-He condition on this edge
            f[2,0,j] = f[4,0,j] + (2./3.)*rho[0,j]*v[0,j]
            f[5,0,j] = f[7,0,j] - 0.5*(f[1,0,j]-f[3,0,j]) + 0.5*rho[0,j]*u[0,j] + (1./6.)*rho[0,j]*v[0,j]
            f[6,0,j] = f[8,0,j] + 0.5*(f[1,0,j]-f[3,0,j]) - 0.5*rho[0,j]*u[0,j] + (1./6.)*rho[0,j]*v[0,j]
    return f, rho, u, v

def simulate_flow(grid_map, max_iter=5000, tau=0.7, inlet_velocity=0.05, convergence_threshold=1e-6,
                 convergence_window=50, use_multiprocessing=False, num_processes=None):
    """
    使用格子玻尔兹曼方法(LBM)模拟流体流动（优化版本）

    参数:
        grid_map: 网格地图，0表示流体，1表示障碍物
        max_iter: 最大迭代次数，默认5000
        tau: 松弛时间，控制流体粘性，默认0.7
             - 较小的tau值对应较低的粘性，流体流动更快但可能不稳定
             - 较大的tau值对应较高的粘性，流体流动较慢但更稳定
        inlet_velocity: 入口速度大小，默认0.05
             - 在LBM中，通常保持在0.1以下以确保数值稳定性
        convergence_threshold: 收敛判断阈值，默认1e-6
             - 当速度变化小于此值时认为收敛
        convergence_window: 收敛判断窗口大小，默认50
             - 连续多少次迭代都满足收敛条件才停止
        use_multiprocessing: 是否使用多进程加速，默认False
        num_processes: 进程数量，默认为None（使用CPU核心数）

    返回:
        u, v: 速度场分量（水平和垂直方向）
        rho: 密度场
        speed: 速度大小场（u和v的平方和的平方根）
        vorticity: 涡量场（速度场的旋度）
        convergence_history: 收敛历史记录（每次迭代的速度变化）

    说明:
        - 使用D2Q9模型进行格子玻尔兹曼方法模拟
        - LBM模拟过程包含三个主要步骤：
          1. 碰撞步骤：粒子分布函数向平衡状态松弛
          2. 流动步骤：粒子沿离散速度方向传播
          3. 边界处理：应用反弹边界条件和Zou-He边界条件
        - 支持多进程加速大规模计算，适用于大型网格
        - 使用滑动窗口判断收敛性，提高收敛判断的稳健性
    """
    start_time = time.time()
    nx, ny = grid_map.shape
    f = np.ones((9, nx, ny)) / 9.0
    np.random.seed(42)
    f += 0.001 * np.random.randn(9, nx, ny) / 9.0

    rho = np.ones((nx, ny))
    u = np.zeros((nx, ny))
    v = np.zeros((nx, ny))

    convergence_history = []
    velocity_window = []

    if use_multiprocessing:
        if num_processes is None:
            num_processes = mp.cpu_count()
        print(f"使用多进程加速，进程数: {num_processes}")
        pool = mp.Pool(processes=num_processes)

    for step in range(max_iter):
        if step > 0:
            u_old = u.copy()
            v_old = v.copy()

        rho = np.sum(f, axis=0)
        u.fill(0) # Reset u
        v.fill(0) # Reset v

        for i in range(9):
            u += c[i,0] * f[i]
            v += c[i,1] * f[i]

        mask = (rho > 1e-10)
        u[mask] = u[mask] / rho[mask]
        v[mask] = v[mask] / rho[mask]

        speed_limit = 0.3
        speed_magnitude = np.sqrt(u**2 + v**2)
        speed_mask = speed_magnitude > speed_limit
        if np.any(speed_mask):
            non_zero_speed_mask = speed_mask & (speed_magnitude > 1e-9)
            u[non_zero_speed_mask] = u[non_zero_speed_mask] * speed_limit / speed_magnitude[non_zero_speed_mask]
            v[non_zero_speed_mask] = v[non_zero_speed_mask] * speed_limit / speed_magnitude[non_zero_speed_mask]

        f, rho, u, v = apply_zou_he_boundary(f, rho, u, v, grid_map, inlet_velocity)

        obstacle_mask = (grid_map == 1)
        u[obstacle_mask] = 0
        v[obstacle_mask] = 0
        rho[obstacle_mask] = 1.0 # Density in obstacles often set to average or initial

        rho_flat = rho.flatten()
        u_flat = u.flatten()
        v_flat = v.flatten()

        if use_multiprocessing and nx*ny > 10000:
            chunk_size = len(rho_flat) // num_processes
            chunks = [(rho_flat[i:i+chunk_size], u_flat[i:i+chunk_size], v_flat[i:i+chunk_size])
                      for i in range(0, len(rho_flat), chunk_size)]
            results = pool.starmap(equilibrium, chunks)
            feq_flat = np.hstack(results)
            feq = feq_flat.reshape((9, nx, ny))
        else:
            feq_flat = equilibrium(rho_flat, u_flat, v_flat)
            feq = feq_flat.reshape((9, nx, ny))

        omega = 1.0 / tau
        f_new = f - omega * (f - feq)
        f_new = np.maximum(f_new, 1e-12)
        f = f_new

        for i in range(9):
            f[i] = np.roll(np.roll(f[i], c[i,0], axis=0), c[i,1], axis=1)

        f = bounce_back_optimized(f, grid_map)

        if step > 0:
            # Calculate velocity difference only on fluid nodes
            fluid_nodes_mask = (grid_map == 0)
            if np.sum(fluid_nodes_mask) > 0: # Ensure there are fluid nodes
                 velocity_diff = np.sqrt(np.mean(((u - u_old)**2 + (v - v_old)**2)[fluid_nodes_mask]))
            else:
                velocity_diff = 0 # No fluid nodes, no change

            convergence_history.append(velocity_diff)
            velocity_window.append(velocity_diff)

            if len(velocity_window) > convergence_window:
                velocity_window.pop(0)

            if step % 100 == 0:
                elapsed = time.time() - start_time
                print(f"Iteration {step}, Velocity change: {velocity_diff:.8f}, Time: {elapsed:.2f}s")

            if len(velocity_window) == convergence_window and step > 1000: # Ensure enough steps for stable window
                window_mean = np.mean(velocity_window)
                window_std = np.std(velocity_window)
                if window_mean < convergence_threshold and window_std < convergence_threshold / 2:
                    elapsed = time.time() - start_time
                    print(f"Converged after {step} iterations, Time: {elapsed:.2f}s")
                    break

    if step == max_iter -1:
        print(f"Reached max iterations ({max_iter}) without full convergence criteria met.")


    if use_multiprocessing:
        pool.close()
        pool.join()

    speed = np.sqrt(u**2 + v**2)
    vorticity = np.zeros_like(u)
    if nx > 2 and ny > 2 : # Check if dimensions are large enough for gradient calculation
        vorticity[1:-1, 1:-1] = (v[2:, 1:-1] - v[:-2, 1:-1]) - (u[1:-1, 2:] - u[1:-1, :-2]) # Simplified gradient calculation

    return u, v, rho, speed, vorticity, convergence_history


#################################################
# 第二部分：路径规划算法 (Path Planning Algorithms)
# 描述：包含所有路径规划算法（基于速度场的路径规划、
#      流体增强A*算法）及其辅助函数。
#################################################

def streamline_path_planning(u, v, grid_map, start_pos, end_pos, max_steps=5000, step_size=0.5):
    """
    基于流线的路径规划方法

    参数:
        u, v: 速度场分量（水平和垂直方向）
        grid_map: 网格地图，0表示流体，1表示障碍物
        start_pos: 起点坐标 (row, col)
        end_pos: 终点坐标 (row, col)
        max_steps: 最大步数，默认5000
        step_size: 每步的长度，默认0.5
                  - 较小的步长提高精度但增加计算量
                  - 较大的步长减少计算量但可能降低精度

    返回:
        路径坐标列表，从起点到终点

    说明:
        - 通过跟踪流体流线生成路径，类似于将一个轻质粒子放入流场中观察其运动轨迹
        - 使用四阶龙格-库塔(RK4)方法进行数值积分，这是一种高精度的常微分方程数值求解方法
        - 当路径接近终点或达到最大步数时停止
        - 自动避开障碍物区域，如果遇到障碍物会尝试绕过
        - 在流速很低的区域可能会添加小的随机扰动以避免停滞
    """
    h, w = grid_map.shape
    path = [start_pos]
    current_pos = np.array(start_pos, dtype=float)

    def interpolate_velocity(pos_interp): # Renamed pos to pos_interp to avoid conflict
        x_interp, y_interp = pos_interp # Renamed x, y to x_interp, y_interp
        if not (0 <= x_interp < h -1 and 0 <= y_interp < w -1) : # Ensure within interpolatable bounds
             return 0,0


        x0, y0 = int(x_interp), int(y_interp)
        x1, y1 = x0 + 1, y0 + 1

        # Check if any surrounding points are obstacles or out of bounds
        if grid_map[x0,y0]==1 or grid_map[x0,y1]==1 or \
           grid_map[x1,y0]==1 or grid_map[x1,y1]==1:
            return 0,0 # Stop if near obstacle

        wx = x_interp - x0
        wy = y_interp - y0

        # u corresponds to x-velocity (horizontal), v corresponds to y-velocity (vertical)
        # In LBM, u is horizontal (cols, index 1), v is vertical (rows, index 0)
        # The streamline function uses u for row changes (vertical) and v for col changes (horizontal)
        # This means u from LBM should be v_val here, and v from LBM should be u_val here.
        # Let's assume u,v passed to streamline_path_planning are u_horizontal, v_vertical as per common convention.
        # The interpolate_velocity is accessing u and v fields.
        # If u is horizontal velocity, it influences the y-coordinate (columns) in path.
        # If v is vertical velocity, it influences the x-coordinate (rows) in path.
        # The current_pos is (row, col). So delta_row is related to v, delta_col to u.
        # RK4 step: current_pos += [delta_row, delta_col]
        # delta_row = v_vertical_interpolated, delta_col = u_horizontal_interpolated

        # Correcting interpolation indexing based on (row, col) for u and v fields
        # u[row, col] is horizontal velocity, v[row, col] is vertical velocity
        # So, velocity for column change is u, velocity for row change is v.
        vel_u_interp = (1-wx)*(1-wy)*u[x0,y0] + wx*(1-wy)*u[x1,y0] + \
                       (1-wx)*wy*u[x0,y1] + wx*wy*u[x1,y1]
        vel_v_interp = (1-wx)*(1-wy)*v[x0,y0] + wx*(1-wy)*v[x1,y0] + \
                       (1-wx)*wy*v[x0,y1] + wx*wy*v[x1,y1]

        # Return (velocity for row change, velocity for column change)
        return vel_v_interp, vel_u_interp


    for _ in range(max_steps):
        dist_to_end = np.linalg.norm(current_pos - np.array(end_pos))
        if dist_to_end < 2.0:
            path.append(tuple(map(int, end_pos)))
            break

        # RK4 integration:
        # k1_v, k1_u because interpolate_velocity returns (v_vertical, u_horizontal)
        k1_v, k1_u = interpolate_velocity(current_pos)

        mid_pos1 = current_pos + np.array([k1_v, k1_u]) * step_size / 2.0
        k2_v, k2_u = interpolate_velocity(mid_pos1)

        mid_pos2 = current_pos + np.array([k2_v, k2_u]) * step_size / 2.0
        k3_v, k3_u = interpolate_velocity(mid_pos2)

        end_pos_rk = current_pos + np.array([k3_v, k3_u]) * step_size
        k4_v, k4_u = interpolate_velocity(end_pos_rk)

        # Weighted average for final step components
        delta_row = (k1_v + 2*k2_v + 2*k3_v + k4_v) / 6.0
        delta_col = (k1_u + 2*k2_u + 2*k3_u + k4_u) / 6.0

        current_speed = np.sqrt(delta_row**2 + delta_col**2)

        if current_speed < 1e-6: # Epsilon for speed
            # Attempt to escape stagnation by a small random perturbation
            # This part was in original code; needs careful consideration if it's robust
            delta_row += np.random.uniform(-0.01, 0.01) * step_size
            delta_col += np.random.uniform(-0.01, 0.01) * step_size
            current_speed = np.sqrt(delta_row**2 + delta_col**2)
            if current_speed < 1e-6: # Still too slow, break
                 break


        # Normalize step and apply step_size
        next_step = np.array([delta_row, delta_col]) / current_speed * step_size
        current_pos += next_step

        current_pos[0] = np.clip(current_pos[0], 0, h - 1.001) # Clip to stay within bounds for int conversion
        current_pos[1] = np.clip(current_pos[1], 0, w - 1.001)


        current_pos_int = (int(current_pos[0]), int(current_pos[1]))
        if grid_map[current_pos_int[0], current_pos_int[1]] == 1:
            # Hit obstacle, try to revert and perturb slightly (this is a simple handling)
            if len(path) > 1:
                current_pos = np.array(path[-1], dtype=float) # Revert to last valid point
                # Add small random perturbation to try a different direction next time
                current_pos += np.random.uniform(-step_size/2, step_size/2, 2)
                current_pos[0] = np.clip(current_pos[0], 0, h - 1.001)
                current_pos[1] = np.clip(current_pos[1], 0, w - 1.001)
            else: # Cannot revert if start is on obstacle (should be checked before)
                break
            continue # Skip adding this invalid point

        if not path or path[-1] != current_pos_int: # Avoid duplicate points
             path.append(current_pos_int)


    if path[-1] != tuple(map(int, end_pos)):
        if grid_map[int(end_pos[0]), int(end_pos[1])] == 0:
            path.append(tuple(map(int, end_pos)))
    return path


def fluid_enhanced_astar(grid_map, u, v, rho, start_pos, end_pos, fluid_weight=0.7, diagonal_movement=True):
    """
    结合流体场信息的增强A*算法

    参数:
        grid_map: 网格地图，0表示流体，1表示障碍物
        u, v: 速度场分量（水平和垂直方向）
        rho: 密度场（用于计算压力信息）
        start_pos: 起点坐标 (row, col)
        end_pos: 终点坐标 (row, col)
        fluid_weight: 流体信息权重，范围[0,1]，默认0.7
                     - 值为0时退化为传统A*算法
                     - 值为1时完全依赖流体场信息
                     - 0.5-0.8通常能获得较好的平衡
        diagonal_movement: 是否允许对角线移动，默认True
                          - True时可以沿8个方向移动
                          - False时只能沿4个正交方向移动

    返回:
        路径坐标列表，从起点到终点

    说明:
        - 结合传统A*算法和流体场信息，融合两者优势
        - 代价函数综合考虑三个因素：
          1. 距离代价：传统A*的曼哈顿距离
          2. 速度场代价：优先选择流速较高的区域
          3. 流向一致性：优先选择与流体流向一致的路径
        - fluid_weight参数控制流体信息的影响程度，可根据需要调整
        - 支持对角线移动，使路径更加自然平滑
        - 使用优先队列实现，保证搜索效率
    """
    h, w = grid_map.shape
    speed_field = np.sqrt(u**2 + v**2) # Renamed to avoid conflict with parameter 'speed' in other functions
    # pressure_field = (rho - 1.0) / 3.0 # pressure is not directly used in cost, speed is.

    def heuristic(a, b):
        return abs(a[0] - b[0]) + abs(a[1] - b[1])

    if diagonal_movement:
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
        move_costs = [1.0, 1.0, 1.0, 1.0, 1.414, 1.414, 1.414, 1.414]
    else:
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        move_costs = [1.0, 1.0, 1.0, 1.0]

    open_set = []
    heapq.heappush(open_set, (heuristic(start_pos, end_pos), 0, start_pos)) # f_score, count, position

    came_from = {}
    g_score = {pos: float('inf') for r in range(h) for pos_c in range(w) for pos in [(r,pos_c)]} # Init all to inf
    g_score[start_pos] = 0

    f_score = {pos: float('inf') for r in range(h) for pos_c in range(w) for pos in [(r,pos_c)]}
    f_score[start_pos] = heuristic(start_pos, end_pos)

    open_set_hash = {start_pos} # To quickly check if a node is in the open_set priority queue

    count = 0 # Tie-breaker for priority queue

    while open_set:
        _, _, current = heapq.heappop(open_set)
        open_set_hash.remove(current)

        if current == end_pos:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.append(start_pos)
            return path[::-1]

        for idx, (dr, dc) in enumerate(directions):
            neighbor = (current[0] + dr, current[1] + dc)

            if not (0 <= neighbor[0] < h and 0 <= neighbor[1] < w):
                continue
            if grid_map[neighbor[0], neighbor[1]] == 1:
                continue

            base_move_cost = move_costs[idx]

            # Fluid cost: higher speed = lower cost
            # Normalize speed, e.g. assuming typical max speed is around 0.1-0.2 in LBM units
            # Max speed in LBM simulation is limited (e.g. to 0.3 in equilibrium function)
            max_possible_lbm_speed = 0.3
            neighbor_speed_val = speed_field[neighbor[0], neighbor[1]]

            # Cost increases if moving against the flow, decreases if moving with the flow.
            # Vector from current to neighbor: (dr, dc)
            # Flow vector at neighbor: (v[neighbor], u[neighbor]) (vertical, horizontal components)
            path_vec = np.array([dr, dc])
            flow_vec = np.array([v[neighbor[0], neighbor[1]], u[neighbor[0], neighbor[1]]])

            # Normalize vectors for dot product alignment
            norm_path_vec = np.linalg.norm(path_vec)
            norm_flow_vec = np.linalg.norm(flow_vec)

            alignment_cost = 0
            if norm_path_vec > 1e-6 and norm_flow_vec > 1e-6:
                cos_theta = np.dot(path_vec / norm_path_vec, flow_vec / norm_flow_vec)
                # cos_theta = 1 (aligned), cost should be low
                # cos_theta = -1 (opposed), cost should be high
                alignment_cost = (1 - cos_theta) # Ranges from 0 (aligned) to 2 (opposed)

            # Speed-based cost: higher speed in cell is generally good, but we want to follow streamlines
            # A simple speed factor: cost_speed = 1 / (1 + normalized_speed_val)
            # Let's use alignment primarily, and base_move_cost
            # fluid_cost_factor = (1 - fluid_weight) * base_move_cost + fluid_weight * (base_move_cost * (1 + alignment_cost))
            # A simpler cost:
            # If speed is very low, penalize more.
            speed_factor = 1.0 / (1.0 + 10 * neighbor_speed_val / max_possible_lbm_speed) # Lower cost for higher speed

            # Combined cost:
            # Cost = base_cost * ( (1-w_f) + w_f * (cost_due_to_flow_speed + cost_due_to_alignment) )
            # Cost due to flow: (1 + alignment_cost) * speed_factor
            # alignment_cost is [0,2], speed_factor is approx [0,1]
            # This makes cost higher if misaligned or low speed.
            flow_contribution = (1 + alignment_cost) * speed_factor

            combined_cost = base_move_cost * ((1 - fluid_weight) + fluid_weight * flow_contribution)


            tentative_g_score = g_score[current] + combined_cost

            if tentative_g_score < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                f_score[neighbor] = tentative_g_score + heuristic(neighbor, end_pos)
                if neighbor not in open_set_hash:
                    count += 1
                    heapq.heappush(open_set, (f_score[neighbor], count, neighbor))
                    open_set_hash.add(neighbor)
    return [] # Path not found


def get_default_start_end_points(grid_map):
    """
    获取默认的起点和终点位置

    参数:
        grid_map: 网格地图，0表示流体，1表示障碍物

    返回:
        start_point: 起点位置 (row, col)，默认在右下角（入口）
        end_point: 终点位置 (row, col)，默认在左上角（出口）

    说明:
        - 根据流体模拟的设置，入口在右下角，出口在左上角
        - 流体从右下角流向左上角，所以起点在右下角，终点在左上角
        - 函数会自动寻找靠近这些位置的有效流体点作为起点和终点
        - 如果默认位置是障碍物，会在附近搜索合适的流体点
        - 确保起点和终点都在流体区域内，避免无效路径
    """
    h, w = grid_map.shape

    # 起点在右下角（入口区域）
    # add_boundary函数在右下角创建入口，在左上角创建出口
    # 边界厚度为1，所以(h-2, w-2)是右下角附近最内侧的流体单元之一
    # 而(1,1)是左上角附近最内侧的流体单元之一
    start_point = (h-2, w-2)  # 右下角入口
    end_point = (1, 1)        # 左上角出口

    # 确保起点和终点是流体区域且在边界内
    if not (0 <= start_point[0] < h and 0 <= start_point[1] < w and grid_map[start_point[0], start_point[1]] == 0):
        # Search for a valid fluid start point near the desired corner if default is bad
        for r_offset in range(min(h//2, w//2)):
            found = False
            # Check points forming a square outwards from the corner
            # (h-2, w-2) up to (h-2+r_offset, w-2+r_offset) etc.
            # Simplified: just search a small box
            for i in range(max(h-2-r_offset-5, 0), min(h-1, h-2+r_offset+5)):
                 for j in range(max(w-2-r_offset-5, 0), min(w-1, w-2+r_offset+5)):
                    if 0 <= i < h and 0 <= j < w and grid_map[i,j] == 0:
                        start_point = (i,j)
                        found = True
                        break
                 if found: break
            if found: break
        if grid_map[start_point[0], start_point[1]] == 1: # Still an obstacle
            print("Warning: Could not find a valid fluid start point, using (h-2,w-2) if possible.")
            # Fallback to a point that might be an obstacle but is within bounds
            start_point = (max(0,h-2), max(0,w-2)) if h > 1 and w > 1 else (0,0)


    if not (0 <= end_point[0] < h and 0 <= end_point[1] < w and grid_map[end_point[0], end_point[1]] == 0):
        found = False
        for r_offset in range(min(h//2, w//2)):
            for i in range(max(1, 1 - r_offset -5), min(h-1, 1 - r_offset + 5)):
                for j in range(max(1, 1 - r_offset -5), min(w-1, 1 - r_offset + 5)):
                     if 0 <= i < h and 0 <= j < w and grid_map[i,j] == 0:
                        end_point = (i,j)
                        found = True
                        break
                if found: break
            if found: break
        if grid_map[end_point[0], end_point[1]] == 1:
             print("Warning: Could not find a valid fluid end point, using (1,1) if possible.")
             end_point = (min(h-2, h-1), min(w-2,w-1)) if h > 1 and w > 1 else (0,0)


    return start_point, end_point


#################################################
# 第三部分：工具函数 (Utility Functions)
# 描述：包含通用的辅助函数，如颜色映射创建、雷诺数计算、
#      Excel数据加载以及路径性能评估指标的计算。
# 这些函数为主要算法提供支持，处理数据加载、性能评估等任务。
#################################################

def create_custom_colormap():
    """
    创建自定义颜色映射，用于更好的可视化效果

    返回:
        matplotlib颜色映射对象，从蓝色到红色，中间经过白色
    """
    colors = [(0, 0, 0.8), (0, 0.5, 1), (0.9, 0.9, 1), (1, 0.5, 0), (0.8, 0, 0)]
    return LinearSegmentedColormap.from_list('custom_cmap', colors, N=256)

def calculate_reynolds_number(u_max, characteristic_length, tau):
    """
    计算雷诺数（无量纲参数，表示惯性力与粘性力的比值）

    参数:
        u_max: 最大速度
        characteristic_length: 特征长度（通常为通道宽度）
        tau: 松弛时间

    返回:
        雷诺数
    """
    nu = (tau - 0.5) / 3.0
    if nu <= 1e-6: # Avoid division by zero or very small viscosity
        return float('inf')
    return u_max * characteristic_length / nu

def load_excel_data(file_path='label/sea_ice_419.xlsx'):
    """
    从Excel文件加载数据并转换为二进制地图

    参数:
        file_path: Excel文件路径

    返回:
        二进制地图，0表示流体，1表示障碍物

    说明:
        - 默认读取256×256的数据区域
        - 将值为0的单元格转换为流体(0)，值为255的单元格转换为障碍物(1)
        - 如果文件不存在或格式错误，返回None
    """
    try:
        import pandas as pd
        # Attempt to determine if the file path is relative and adjust if necessary
        # This part is tricky without knowing the execution context.
        # Assuming file_path is correct relative to where the script is run.
        data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
        numpy_array = data.to_numpy()
        # Original logic: 0 -> 255 (fluid?), 255 -> 0 (obstacle?)
        # Standard: 0 for fluid, 1 for obstacle.
        # If Excel 0 means obstacle and 255 means fluid:
        # binary_map = np.where(numpy_array == 0, 1, np.where(numpy_array == 255, 0, numpy_array))
        # If Excel 0 means fluid and 255 means obstacle (more common in image processing for obstacles):
        binary_map = np.where(numpy_array == 255, 1, 0) # Assuming 255 is obstacle, 0 is fluid
        return binary_map.astype(int)
    except Exception as e:
        print(f"Error loading Excel data from {file_path}: {e}")
        return None

def calculate_path_smoothness(path):
    """
    计算路径平滑度（基于曲率变化）
    越接近1越平滑。
    """
    if len(path) < 3:
        return 1.0 # A straight line (or point) is perfectly smooth

    curvatures = []
    for i in range(1, len(path)-1):
        p1 = np.array(path[i-1])
        p2 = np.array(path[i])
        p3 = np.array(path[i+1])

        v1 = p2 - p1
        v2 = p3 - p2

        norm_v1 = np.linalg.norm(v1)
        norm_v2 = np.linalg.norm(v2)

        if norm_v1 > 1e-6 and norm_v2 > 1e-6:
            cos_angle = np.dot(v1, v2) / (norm_v1 * norm_v2)
            cos_angle = np.clip(cos_angle, -1.0, 1.0) # Ensure valid input for arccos
            angle_rad = np.arccos(cos_angle) # Angle is between 0 and pi
            curvatures.append(angle_rad) # Store angle directly (represents change)

    if not curvatures: # Handles cases like a straight line of more than 2 points
        return 1.0

    # Smoothness = 1 - (average turning angle / max possible turning angle (pi))
    # This makes 0 curvature (straight) lead to smoothness 1.
    # Max curvature (sharpest turn like 180 deg) leads to smoothness 0.
    avg_curvature_rad = np.mean(curvatures)
    smoothness = 1.0 - (avg_curvature_rad / np.pi)
    return max(0, smoothness) # Ensure smoothness is not negative

def calculate_average_speed_along_path(path, speed_field):
    """
    计算路径上的平均速度
    """
    if not path:
        return 0.0

    path_speeds = []
    for point in path:
        row, col = int(point[0]), int(point[1])
        if 0 <= row < speed_field.shape[0] and 0 <= col < speed_field.shape[1]:
            path_speeds.append(speed_field[row, col])
        # else: point is out of bounds, maybe skip or assign a penalty

    return np.mean(path_speeds) if path_speeds else 0.0

def calculate_flow_alignment(path, u, v):
    """
    计算路径与流场的一致性
    值域 [0, 1]，1表示完美对齐。
    """
    if len(path) < 2:
        return 0.0 # Cannot determine alignment for a single point or no path

    alignments = []
    for i in range(len(path)-1):
        p1 = np.array(path[i])
        p2 = np.array(path[i+1])

        path_segment_vector = p2 - p1
        norm_path_segment = np.linalg.norm(path_segment_vector)

        if norm_path_segment < 1e-6: # Zero length segment
            continue

        path_direction = path_segment_vector / norm_path_segment

        # Fluid flow vector at the midpoint of the segment
        mid_point_float = (p1 + p2) / 2.0
        mid_row, mid_col = int(mid_point_float[0]), int(mid_point_float[1])

        if not (0 <= mid_row < u.shape[0] and 0 <= mid_col < u.shape[1]):
            continue # Midpoint out of bounds

        # u is horizontal velocity, v is vertical velocity
        # path_direction is (delta_row, delta_col)
        # So, flow_vector should be (v_component, u_component) to match
        flow_vector = np.array([v[mid_row, mid_col], u[mid_row, mid_col]])
        norm_flow_vector = np.linalg.norm(flow_vector)

        if norm_flow_vector < 1e-6: # Zero flow speed at midpoint
            alignments.append(0.0) # Or some neutral value, e.g. 0.5 if not penalized
            continue

        flow_direction = flow_vector / norm_flow_vector

        # Cosine of the angle between path segment and flow direction
        # Max value 1 (perfectly aligned), min value -1 (perfectly opposed)
        # We want a metric from 0 to 1 where 1 is good.
        # (cos(theta) + 1) / 2 maps [-1, 1] to [0, 1]
        alignment_cosine = np.dot(path_direction, flow_direction)
        alignments.append((alignment_cosine + 1.0) / 2.0)

    return np.mean(alignments) if alignments else 0.0


def calculate_energy_efficiency(path, u, v, rho):
    """
    计算能量效率（基于压力损失和速度利用）
    简化的能量度量，值越高越好。
    """
    if not path:
        return 0.0

    total_inverse_energy_metric = 0
    num_points = 0

    for point in path:
        row, col = int(point[0]), int(point[1])
        if not (0 <= row < rho.shape[0] and 0 <= col < rho.shape[1]):
            continue

        num_points+=1
        # Kinetic energy per unit mass = 0.5 * (u^2 + v^2)
        kinetic_energy_density = 0.5 * (u[row, col]**2 + v[row, col]**2)

        # Pressure (deviation from reference rho=1, c_s^2 = 1/3)
        # Pressure p = (rho - rho_ref) * c_s^2. Here, effectively rho itself if rho_ref=0 or (rho-1)*c_s^2
        # Let's consider pressure as rho directly or (rho-1)/3.0
        pressure_val = (rho[row, col] - 1.0) / 3.0 # More like pressure fluctuation

        # A simple metric: we want high speed (kinetic energy) and low adverse pressure
        # Higher speed is good. Lower |pressure_val| could mean less effort against pressure gradients.
        # This definition is a bit arbitrary.
        # Original: efficiency = 1.0 / (1.0 + avg_energy) where avg_energy = K.E + |pressure_energy|
        # This means lower K.E and lower pressure_energy means higher efficiency.
        # This might be counter-intuitive if high speed is desired.
        # Let's re-evaluate: if we want to maximize speed and minimize "effort" (e.g. pressure work)
        # Let's stick to the original formula's implication: lower "total_energy" sum is better.
        point_energy = kinetic_energy_density + abs(pressure_val)
        total_inverse_energy_metric += point_energy

    if num_points == 0:
        return 0.0

    avg_point_energy = total_inverse_energy_metric / num_points

    # Higher efficiency if avg_point_energy is lower.
    # Normalize to [0,1] range; 1 / (1 + X) is a common way.
    efficiency = 1.0 / (1.0 + avg_point_energy)
    return efficiency


#################################################
# 第四部分：可视化与结果分析 (Visualization and Results Analysis)
# 描述：包含所有绘图、可视化和结果展示相关的函数。
# 这部分负责将模拟结果和路径规划结果以图形方式呈现，
# 并提供详细的性能分析和比较报告。所有可视化使用
# Times New Roman字体，确保专业的展示效果。
#################################################

def visualize_results(grid_map, u, v, rho, speed, vorticity, convergence_history):
    """
    可视化模拟结果，包括速度场、压力场、涡量场、流线和收敛历史

    参数:
        grid_map: 网格地图，0表示流体，1表示障碍物
        u, v: 速度场分量
        rho: 密度场
        speed: 速度大小场
        vorticity: 涡量场
        convergence_history: 收敛历史记录

    返回:
        matplotlib figure对象
    """
    custom_cmap = create_custom_colormap()
    fig = plt.figure(figsize=(20, 15), dpi=100)

    # 1. 速度大小场
    ax1 = fig.add_subplot(331)
    im1 = ax1.imshow(speed.T, cmap=custom_cmap, origin='lower', vmin=0, vmax=np.percentile(speed[grid_map==0], 99) if np.any(grid_map==0) else np.max(speed))
    cbar1 = plt.colorbar(im1, ax=ax1)
    cbar1.set_label('Velocity Magnitude')
    ax1.imshow(grid_map.T, cmap='gray_r', alpha=0.3, origin='lower')
    ax1.set_title('Velocity Magnitude Field')
    ax1.set_xlabel('X Position')
    ax1.set_ylabel('Y Position')

    # 2. 压力场 (密度场)
    ax2 = fig.add_subplot(332)
    pressure = (rho - 1.0) / 3.0 # LBM pressure p = rho * c_s^2, with c_s^2=1/3
    im2 = ax2.imshow(pressure.T, cmap='coolwarm', origin='lower', vmin=np.percentile(pressure[grid_map==0],1) if np.any(grid_map==0) else np.min(pressure), vmax=np.percentile(pressure[grid_map==0],99) if np.any(grid_map==0) else np.max(pressure))
    cbar2 = plt.colorbar(im2, ax=ax2)
    cbar2.set_label('Pressure (p - p_atm)')
    ax2.imshow(grid_map.T, cmap='gray_r', alpha=0.3, origin='lower')
    ax2.set_title('Pressure Field')
    ax2.set_xlabel('X Position')
    ax2.set_ylabel('Y Position')

    # 3. 涡量场
    ax3 = fig.add_subplot(333)
    vort_abs_max = np.percentile(np.abs(vorticity[grid_map==0]), 99) if np.any(grid_map==0) else np.max(np.abs(vorticity))
    im3 = ax3.imshow(vorticity.T, cmap='seismic', origin='lower', vmin=-vort_abs_max, vmax=vort_abs_max)
    cbar3 = plt.colorbar(im3, ax=ax3)
    cbar3.set_label('Vorticity')
    ax3.imshow(grid_map.T, cmap='gray_r', alpha=0.3, origin='lower')
    ax3.set_title('Vorticity Field')
    ax3.set_xlabel('X Position')
    ax3.set_ylabel('Y Position')

    # 4. 3D速度场表面图 (替换原来的速度矢量场)
    ax4 = fig.add_subplot(334, projection='3d')
    skip_3d = max(1, min(speed.shape) // 50)
    x, y = np.meshgrid(np.arange(0, u.shape[1]), np.arange(0, u.shape[0]))
    surf = ax4.plot_surface(x[::skip_3d, ::skip_3d], y[::skip_3d, ::skip_3d], speed[::skip_3d, ::skip_3d],
                           cmap=custom_cmap, linewidth=0, antialiased=True)
    cbar4 = plt.colorbar(surf, ax=ax4, shrink=0.5, aspect=10)
    cbar4.set_label('Velocity Magnitude')
    ax4.set_title('3D Velocity Field')
    ax4.set_xlabel('X Position'); ax4.set_ylabel('Y Position'); ax4.set_zlabel('Velocity')


    # 5. 流线
    ax5 = fig.add_subplot(335)
    # Streamplot expects x, y, u (x-comp), v (y-comp)
    # u is horizontal velocity, v is vertical velocity
    streamplot = ax5.streamplot(x, y, u, v, density=1.5, color=speed, cmap=custom_cmap, linewidth=1.0)
    cbar5 = plt.colorbar(streamplot.lines, ax=ax5)
    cbar5.set_label('Velocity Magnitude')
    ax5.imshow(grid_map.T, cmap='gray_r', alpha=0.3, origin='lower')
    ax5.set_title('Streamlines')
    ax5.set_xlabel('X Position'); ax5.set_ylabel('Y Position')
    ax5.set_aspect('equal')

    # 6. 收敛历史
    ax6 = fig.add_subplot(336)
    ax6.semilogy(convergence_history)
    ax6.set_xlabel('Iteration')
    ax6.set_ylabel('RMS Velocity Change (Error)')
    ax6.set_title('Convergence History')
    ax6.grid(True, which="both", ls="-")


    # 7. 3D速度场表面图
    ax7 = fig.add_subplot(337, projection='3d')
    skip_3d = max(1, min(speed.shape) // 50)
    surf = ax7.plot_surface(x[::skip_3d, ::skip_3d], y[::skip_3d, ::skip_3d], speed[::skip_3d, ::skip_3d],
                           cmap=custom_cmap, linewidth=0, antialiased=True)
    cbar7 = plt.colorbar(surf, ax=ax7, shrink=0.5, aspect=10)
    cbar7.set_label('Velocity Magnitude')
    ax7.set_title('3D Velocity Field')
    ax7.set_xlabel('X Position'); ax7.set_ylabel('Y Position'); ax7.set_zlabel('Velocity')

    # 8. 障碍物地图
    ax8 = fig.add_subplot(338)
    ax8.imshow(grid_map.T, cmap='gray_r', origin='lower')
    ax8.set_title('Obstacle Map')
    ax8.set_xlabel('X Position'); ax8.set_ylabel('Y Position')

    # 9. 组合可视化 (速度 + 流线)
    ax9 = fig.add_subplot(339)
    im9 = ax9.imshow(speed.T, cmap=custom_cmap, origin='lower', vmin=0, vmax=np.percentile(speed[grid_map==0], 99) if np.any(grid_map==0) else np.max(speed), alpha=0.7)
    ax9.streamplot(x, y, u, v, density=1.0, color='white', linewidth=0.5, arrowsize=0.7)
    ax9.imshow(grid_map.T, cmap='gray_r', alpha=0.3, origin='lower')
    cbar9 = plt.colorbar(im9, ax=ax9)
    cbar9.set_label('Velocity Magnitude')
    ax9.set_title('Combined Visualization (Velocity + Streamlines)')
    ax9.set_xlabel('X Position'); ax9.set_ylabel('Y Position')
    ax9.set_aspect('equal')


    plt.tight_layout(pad=2.0)

    # Save velocity field data to Excel file (optional)
    try:
        import pandas as pd
        df_speed = pd.DataFrame(speed) # Save speed field
        df_speed.to_excel('velocity_field_speed.xlsx', index=False, header=False)
        print("Velocity field data (speed) saved to 'velocity_field_speed.xlsx'")
    except ImportError:
        print("Pandas not installed. Could not save velocity field data to Excel.")
    except Exception as e:
        print(f"Could not save velocity field data to Excel: {e}")

    fig.savefig('fluid_simulation_results.png', dpi=300, bbox_inches='tight')
    print("Visualization saved to 'fluid_simulation_results.png'")
    return fig


def analyze_path_performance(streamline_path, astar_path, u, v, rho, speed_field,
                           streamline_time, astar_time): # Renamed speed to speed_field
    """
    分析两种路径规划方法的详细性能指标

    参数:
    streamline_path, astar_path: 两种方法的路径
    u, v: 速度场分量
    rho: 密度场
    speed_field: 速度大小场 (renamed from speed to avoid conflict)
    streamline_time, astar_time: 计算时间

    返回:
    performance_results: 性能分析结果字典
    """
    results = {
        'streamline': {'path': streamline_path, 'time': streamline_time, 'name': 'Streamline'},
        'astar': {'path': astar_path, 'time': astar_time, 'name': 'Enhanced A*'}
    }

    for method_name, method_data in results.items():
        path = method_data['path']
        if not path or len(path) < 2: # Path needs at least 2 points for metrics
            method_data.update({
                'length': 0, 'smoothness': 0, 'avg_speed': 0,
                'flow_alignment': 0, 'energy_efficiency': 0, 'path_points': len(path) if path else 0
            })
            continue

        path_length = sum(np.linalg.norm(np.array(path[i+1]) - np.array(path[i]))
                         for i in range(len(path)-1))
        smoothness = calculate_path_smoothness(path)
        avg_speed_val = calculate_average_speed_along_path(path, speed_field) # Use speed_field
        flow_alignment_val = calculate_flow_alignment(path, u, v)
        energy_efficiency_val = calculate_energy_efficiency(path, u, v, rho)

        method_data.update({
            'length': path_length, 'smoothness': smoothness, 'avg_speed': avg_speed_val,
            'flow_alignment': flow_alignment_val, 'energy_efficiency': energy_efficiency_val,
            'path_points': len(path)
        })
    return results


def print_performance_report(results):
    """
    打印详细的性能分析报告
    """
    print("\n" + "="*70)
    print("           详细路径规划性能分析报告 (Detailed Path Planning Performance Report)")
    print("="*70)

    # 表头
    header = f"{'指标 (Metric)':<25} | {'流线方法 (Streamline)':<20} | {'增强A* (Enhanced A*)':<20}"
    print(header)
    print("-" * len(header))

    metrics_display = [
        ('计算时间 (秒) (Comp. Time (s))', 'time', '.4f', False), # Smaller is better
        ('路径点数 (Path Points)', 'path_points', 'd', False),    # Informative
        ('路径长度 (Path Length)', 'length', '.2f', False),        # Smaller is better (typically)
        ('路径平滑度 (Path Smoothness)', 'smoothness', '.3f', True), # Larger is better
        ('平均速度 (Average Speed)', 'avg_speed', '.4f', True),    # Larger is better
        ('流向一致性 (Flow Alignment)', 'flow_alignment', '.3f', True), # Larger is better
        ('能量效率 (Energy Efficiency)', 'energy_efficiency', '.3f', True) # Larger is better
    ]

    data_streamline = results['streamline']
    data_astar = results['astar']

    for display_name, key, fmt, higher_is_better in metrics_display:
        val_s = data_streamline.get(key, float('nan'))
        val_a = data_astar.get(key, float('nan'))

        # Determine winner, handling NaN cases
        winner = "N/A"
        if not (np.isnan(val_s) or np.isnan(val_a)):
            if higher_is_better:
                if val_s > val_a: winner = data_streamline['name']
                elif val_a > val_s: winner = data_astar['name']
                else: winner = "相等 (Equal)"
            else: # Lower is better
                if val_s < val_a: winner = data_streamline['name']
                elif val_a < val_s: winner = data_astar['name']
                else: winner = "相等 (Equal)"
        elif not np.isnan(val_s): winner = data_streamline['name']
        elif not np.isnan(val_a): winner = data_astar['name']

        # Format values, handling potential NaN from missing paths
        s_str = f"{val_s:{fmt}}" if not np.isnan(val_s) else "N/A"
        a_str = f"{val_a:{fmt}}" if not np.isnan(val_a) else "N/A"

        print(f"{display_name:<25} | {s_str:<20} | {a_str:<20}")
    print("-" * len(header))

    # 综合评价
    print("\n综合评价 (Overall Evaluation):")
    print(f"• {data_streamline['name']}: 通常计算速度较快，路径较为自然，依赖流场质量。")
    print(f"  (Typically faster computation, more natural paths, depends on flow field quality.)")
    print(f"• {data_astar['name']}: 考虑多种因素优化路径，可能更优但计算成本较高。")
    print(f"  (Optimizes path considering multiple factors, potentially better but computationally more expensive.)")

    print("\n推荐使用场景 (Recommended Usage Scenarios):")
    # Example recommendations based on a few metrics
    if data_streamline['time'] < data_astar['time'] * 0.5 and not (np.isnan(data_streamline['time']) or np.isnan(data_astar['time'])):
        print(f"• 实时性要求高 (High real-time requirements): 推荐 {data_streamline['name']}")
    if not (np.isnan(data_astar['flow_alignment']) or np.isnan(data_streamline['flow_alignment'])) and data_astar['flow_alignment'] > data_streamline['flow_alignment'] * 1.1:
         print(f"• 高流向一致性优先 (High flow alignment priority): 推荐 {data_astar['name']}")
    if not (np.isnan(data_streamline['smoothness']) or np.isnan(data_astar['smoothness'])) and data_streamline['smoothness'] > data_astar['smoothness'] * 1.1:
        print(f"• 路径平滑度优先 (Path smoothness priority): 推荐 {data_streamline['name']}")
    print("="*70)


#################################################
# 第五部分：应用封装、示例与主函数
# (Application Encapsulation, Examples, and Main Function)
# 描述：包含核心控制类FluidPathPlanner，示例用法以及主执行函数。
# 这部分将前面所有功能整合成一个易于使用的接口，提供完整的
# 工作流程，从地图加载、流体模拟到路径规划和结果可视化。
# 用户可以通过调整主函数中的参数来自定义程序行为。
#################################################

class FluidPathPlanner:
    """
    流体路径规划器类，封装所有功能

    主要特点:
    1. 加载和处理地图 - 支持从Excel文件加载或使用自定义地图
    2. 运行流体模拟 - 使用LBM方法模拟流体在障碍物周围的流动
    3. 使用不同方法执行路径规划 - 包括流线跟踪和流体增强A*算法
    4. 可视化和比较结果 - 生成详细的可视化图表和性能分析报告

    使用流程:
    1. 创建FluidPathPlanner实例，提供地图和可选的起终点
    2. 调用run_fluid_simulation()进行流体模拟
    3. 调用find_path_streamline()和/或find_path_fluid_astar()进行路径规划
    4. 调用compare_paths_and_visualize()比较和可视化结果

    这个类提供了一个统一的接口，使用户能够方便地使用和比较不同的
    路径规划方法，无需直接处理底层的复杂实现细节。
    """

    def __init__(self, grid_map_input=None, start_point=None, end_point=None): # Renamed grid_map to grid_map_input
        """
        初始化流体路径规划器

        参数:
            grid_map_input: 网格地图，0表示流体，1表示障碍物
            start_point: 起点坐标 (row, col)，默认为None (自动检测)
            end_point: 终点坐标 (row, col)，默认为None (自动检测)
        """
        self.original_grid_map = grid_map_input
        self.grid_map_with_boundary = None # This will be the map used for simulation
        self.user_start_point = start_point # Store user-defined points
        self.user_end_point = end_point
        self.sim_start_point = None # Points adjusted for the boundary-added map
        self.sim_end_point = None


        # 流体场数据
        self.u = None; self.v = None; self.rho = None
        self.speed = None; self.vorticity = None; self.convergence_history = None

        # 路径数据
        self.streamline_path = None; self.astar_path = None

        if self.original_grid_map is not None:
            self.prepare_grid_with_inlet_outlet()


    def load_map_from_excel(self, file_path='label/sea_ice_419.xlsx'):
        """
        从Excel文件加载地图

        参数:
            file_path: Excel文件路径

        返回:
            True 如果加载成功，否则 False
        """
        self.original_grid_map = load_excel_data(file_path)
        if self.original_grid_map is not None:
            self.prepare_grid_with_inlet_outlet()
            return True
        print("错误: Excel地图加载失败。")
        return False

    def prepare_grid_with_inlet_outlet(self):
        """
        准备网格，添加边界、入口和出口，并调整起点/终点
        """
        if self.original_grid_map is None:
            print("错误: 地图未加载，无法准备网格。")
            return

        self.grid_map_with_boundary = add_boundary(self.original_grid_map)

        # Adjust start/end points if they were given for the original map
        # The add_boundary function adds (extension_size + 1) padding on all sides.
        h_orig, w_orig = self.original_grid_map.shape
        extension_size = max(int(min(h_orig, w_orig) * 0.1), 1)
        padding = extension_size + 1

        if self.user_start_point:
            self.sim_start_point = (self.user_start_point[0] + padding, self.user_start_point[1] + padding)
        if self.user_end_point:
            self.sim_end_point = (self.user_end_point[0] + padding, self.user_end_point[1] + padding)

        # If sim_start_point or sim_end_point are still None, get defaults for the *boundary-added* map
        # get_default_start_end_points expects the map it will operate on.
        default_sim_start, default_sim_end = get_default_start_end_points(self.grid_map_with_boundary)

        if self.sim_start_point is None:
            self.sim_start_point = default_sim_start
        if self.sim_end_point is None:
            self.sim_end_point = default_sim_end

        print(f"原始地图尺寸: {self.original_grid_map.shape}")
        print(f"带边界地图尺寸: {self.grid_map_with_boundary.shape}")
        print(f"模拟用起点: {self.sim_start_point}, 终点: {self.sim_end_point}")


    def run_fluid_simulation(self, max_iter=5000, tau=0.7, inlet_velocity=0.05,
                           convergence_threshold=1e-6, use_multiprocessing=False, num_processes=None):
        """
        运行流体模拟

        参数与 simulate_flow 函数相同。
        """
        if self.grid_map_with_boundary is None:
            print("错误: 网格未准备好，无法运行模拟。")
            return False

        print("开始LBM流体模拟...")
        sim_results = simulate_flow(
            self.grid_map_with_boundary, max_iter, tau, inlet_velocity,
            convergence_threshold, 50, use_multiprocessing, num_processes # convergence_window fixed at 50
        )

        if sim_results:
            self.u, self.v, self.rho, self.speed, self.vorticity, self.convergence_history = sim_results
            print("LBM模拟完成。")

            # Calculate Reynolds number for context
            # Characteristic length can be e.g. original map width or a typical obstacle size
            if self.original_grid_map is not None:
                 char_length = min(self.original_grid_map.shape)
            else: # Fallback if original_grid_map somehow not set but boundary_map is
                 char_length = min(self.grid_map_with_boundary.shape) / 2.0

            if self.speed is not None and np.any(self.speed > 0) : # Check if speed is valid
                max_speed_in_flow = np.max(self.speed[self.grid_map_with_boundary == 0]) if np.any(self.grid_map_with_boundary == 0) else 0
                re = calculate_reynolds_number(max_speed_in_flow, char_length, tau)
                print(f"估算雷诺数 (Estimated Reynolds Number): {re:.2f} (基于最大流体速度和特征长度)")
            else:
                print("速度场数据异常，无法计算雷诺数。")
            return True
        else:
            print("LBM模拟失败。")
            return False


    def visualize_fluid_fields(self):
        """
        可视化流体场
        """
        if self.u is None or self.grid_map_with_boundary is None:
            print("错误: 流体模拟未运行或网格不存在。")
            return None
        print("生成流体场可视化...")
        return visualize_results(
            self.grid_map_with_boundary, self.u, self.v, self.rho,
            self.speed, self.vorticity, self.convergence_history
        )

    def find_path_streamline(self, max_steps=5000, step_size=0.5):
        """
        使用流线跟踪方法寻找路径
        """
        if self.u is None or self.grid_map_with_boundary is None or self.sim_start_point is None or self.sim_end_point is None:
            print("错误: 模拟数据或起/终点未准备好，无法进行流线路径规划。")
            return None
        print(f"开始流线路径规划: 从 {self.sim_start_point} 到 {self.sim_end_point}")
        self.streamline_path = streamline_path_planning(
            self.u, self.v, self.grid_map_with_boundary,
            self.sim_start_point, self.sim_end_point, max_steps, step_size
        )
        if self.streamline_path:
            print(f"流线路径找到，点数: {len(self.streamline_path)}")
        else:
            print("未能找到流线路径。")
        return self.streamline_path


    def find_path_fluid_astar(self, fluid_weight=0.7, diagonal_movement=True):
        """
        使用流体增强A*算法寻找路径
        """
        if self.u is None or self.grid_map_with_boundary is None or self.sim_start_point is None or self.sim_end_point is None:
            print("错误: 模拟数据或起/终点未准备好，无法进行A*路径规划。")
            return None
        print(f"开始流体增强A*路径规划: 从 {self.sim_start_point} 到 {self.sim_end_point}")
        self.astar_path = fluid_enhanced_astar(
            self.grid_map_with_boundary, self.u, self.v, self.rho,
            self.sim_start_point, self.sim_end_point,
            fluid_weight, diagonal_movement
        )
        if self.astar_path:
            print(f"A*路径找到，点数: {len(self.astar_path)}")
        else:
            print("未能找到A*路径。")
        return self.astar_path


    def compare_paths_and_visualize(self):
        """
        比较不同路径规划方法的结果并可视化

        返回:
            matplotlib figure 对象和路径字典
        """
        if self.u is None: # Basic check if simulation has run
            print("错误: 流体模拟未运行，无法比较路径。")
            return None, {}

        # Ensure paths are computed if not already
        if self.streamline_path is None:
            self.find_path_streamline()
        if self.astar_path is None:
            self.find_path_fluid_astar()

        # Assuming streamline_time and astar_time are not measured here, pass 0
        # Actual timing should be done around find_path_streamline/astar calls if needed for report
        performance_results = analyze_path_performance(
            self.streamline_path, self.astar_path,
            self.u, self.v, self.rho, self.speed,
            0, 0 # Placeholder for computation times
        )
        print_performance_report(performance_results)

        # Visualization part
        fig = plt.figure(figsize=(18, 10), dpi=100)
        custom_cmap = create_custom_colormap()

        # Plotting setup (common elements)
        plot_map_display = self.grid_map_with_boundary.T # Transpose for imshow if u,v are (row,col) indexed
        speed_display = self.speed.T
        plot_origin = 'lower'
        percentile_vmax = np.percentile(self.speed[self.grid_map_with_boundary==0], 99) if np.any(self.grid_map_with_boundary==0) else np.max(self.speed)


        # 1. Streamline Path Visualization
        ax1 = fig.add_subplot(231)
        ax1.imshow(speed_display, cmap=custom_cmap, origin=plot_origin, vmin=0, vmax=percentile_vmax, alpha=0.7)
        ax1.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3)
        if self.streamline_path:
            path_y = [p[0] for p in self.streamline_path] # rows
            path_x = [p[1] for p in self.streamline_path] # cols
            ax1.plot(path_x, path_y, 'r-', linewidth=2, label='Streamline Path')
            ax1.plot(path_x[0], path_y[0], 'go', markersize=8, label='Start') # Start of path
            ax1.plot(path_x[-1], path_y[-1], 'mo', markersize=8, label='End')  # End of path
        ax1.set_title('Streamline Path'); ax1.set_xlabel('X'); ax1.set_ylabel('Y'); ax1.legend(); ax1.set_aspect('equal')

        # 2. A* Path Visualization
        ax2 = fig.add_subplot(232)
        ax2.imshow(speed_display, cmap=custom_cmap, origin=plot_origin, vmin=0, vmax=percentile_vmax, alpha=0.7)
        ax2.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3)
        if self.astar_path:
            path_y = [p[0] for p in self.astar_path]
            path_x = [p[1] for p in self.astar_path]
            ax2.plot(path_x, path_y, 'b-', linewidth=2, label='A* Path')
            ax2.plot(path_x[0], path_y[0], 'go', markersize=8, label='Start')
            ax2.plot(path_x[-1], path_y[-1], 'mo', markersize=8, label='End')
        ax2.set_title('Fluid-Enhanced A* Path'); ax2.set_xlabel('X'); ax2.set_ylabel('Y'); ax2.legend(); ax2.set_aspect('equal')

        # 3. Path Comparison on one plot
        ax3 = fig.add_subplot(233)
        ax3.imshow(speed_display, cmap=custom_cmap, origin=plot_origin, vmin=0, vmax=percentile_vmax, alpha=0.5)
        ax3.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3)
        if self.streamline_path:
            path_y_s = [p[0] for p in self.streamline_path]; path_x_s = [p[1] for p in self.streamline_path]
            ax3.plot(path_x_s, path_y_s, 'r-', linewidth=2, label='Streamline Path', alpha=0.8)
        if self.astar_path:
            path_y_a = [p[0] for p in self.astar_path]; path_x_a = [p[1] for p in self.astar_path]
            ax3.plot(path_x_a, path_y_a, 'b--', linewidth=2, label='A* Path', alpha=0.8)
        # Mark common start/end if they are the same
        if self.sim_start_point: ax3.plot(self.sim_start_point[1], self.sim_start_point[0], 'go', markersize=10, label='Sim Start')
        if self.sim_end_point: ax3.plot(self.sim_end_point[1], self.sim_end_point[0], 'mo', markersize=10, label='Sim End')
        ax3.set_title('Path Comparison'); ax3.set_xlabel('X'); ax3.set_ylabel('Y'); ax3.legend(); ax3.set_aspect('equal')

        # 4. Velocity Field (reference)
        ax4 = fig.add_subplot(234)
        im4 = ax4.imshow(speed_display, cmap=custom_cmap, origin=plot_origin, vmin=0, vmax=percentile_vmax)
        plt.colorbar(im4, ax=ax4, label='Velocity Magnitude')
        ax4.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3)
        ax4.set_title('Velocity Field'); ax4.set_xlabel('X'); ax4.set_ylabel('Y'); ax4.set_aspect('equal')

        # 5. Pressure Field (reference)
        ax5 = fig.add_subplot(235)
        pressure_display = ((self.rho - 1.0) / 3.0).T
        im5 = ax5.imshow(pressure_display, cmap='coolwarm', origin=plot_origin)
        plt.colorbar(im5, ax=ax5, label='Pressure')
        ax5.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3)
        ax5.set_title('Pressure Field'); ax5.set_xlabel('X'); ax5.set_ylabel('Y'); ax5.set_aspect('equal')

        # 6. Streamlines (reference)
        ax6 = fig.add_subplot(236)
        h_plot, w_plot = self.grid_map_with_boundary.shape # h_plot=rows, w_plot=cols
        y_mesh, x_mesh = np.mgrid[0:h_plot:1, 0:w_plot:1] # y_mesh for rows, x_mesh for columns
        # streamplot takes x, y, u_x, u_y.
        # Our u is horizontal velocity, v is vertical velocity
        # So, x_mesh, y_mesh, self.u, self.v
        streamplot_obj = ax6.streamplot(x_mesh, y_mesh, self.u.T, self.v.T, density=1.5, color=speed_display, cmap=custom_cmap, linewidth=1.0)
        plt.colorbar(streamplot_obj.lines, ax=ax6, label='Speed')
        ax6.imshow(plot_map_display, cmap='gray_r', origin=plot_origin, alpha=0.3) # Use gray for obstacles
        ax6.set_title('Streamlines'); ax6.set_xlabel('X'); ax6.set_ylabel('Y'); ax6.set_aspect('equal')

        plt.tight_layout(pad=2.0)
        fig.savefig('path_comparison_visualization.png', dpi=300, bbox_inches='tight')
        print("路径比较可视化已保存到 'path_comparison_visualization.png'")

        return fig, {'streamline': self.streamline_path, 'astar': self.astar_path}


def example_usage():
    """
    示例：如何使用FluidPathPlanner类

    这个函数展示了FluidPathPlanner类的完整使用流程，包括：
    1. 创建测试地图
    2. 初始化规划器
    3. 运行流体模拟
    4. 可视化流体场
    5. 使用不同方法进行路径规划
    6. 比较和可视化路径结果

    用户可以通过这个示例了解整个系统的工作方式，
    并作为自己使用该库的参考。
    """
    print("\n=== 流体路径规划器示例 (Fluid Path Planner Example) ===")

    # 创建一个简单的测试地图
    grid_size = 30 # Smaller for quicker example
    test_map = np.zeros((grid_size, grid_size), dtype=int)
    test_map[5:25, 15] = 1  # 垂直墙
    test_map[12, 5:25] = 1  # 水平墙
    np.random.seed(42)
    for _ in range(grid_size // 2): # Fewer random obstacles
        x_rand, y_rand = np.random.randint(0, grid_size, 2)
        test_map[x_rand, y_rand] = 1

    # 清理起点和终点区域 (e.g. ensure corners of original map are clear)
    test_map[0,0] = 0; test_map[0,1]=0; test_map[1,0]=0; test_map[1,1]=0;
    test_map[grid_size-1, grid_size-1]=0; test_map[grid_size-2, grid_size-1]=0;
    test_map[grid_size-1, grid_size-2]=0; test_map[grid_size-2, grid_size-2]=0;


    # 定义原始地图的起点和终点 (可选, None会让planner使用默认)
    # Example: bottom-right to top-left of original map
    # user_start = (grid_size - 3, grid_size - 3)
    # user_end = (2, 2)
    user_start = None # Let planner use defaults based on inlet/outlet
    user_end = None


    # 创建规划器实例
    planner = FluidPathPlanner(grid_map_input=test_map, start_point=user_start, end_point=user_end)

    # 1. 运行流体模拟
    print("\n1. 运行流体模拟...")
    sim_success = planner.run_fluid_simulation(
        max_iter=1500, tau=0.65, inlet_velocity=0.04, # Faster sim for example
        convergence_threshold=5e-5, use_multiprocessing=False
    )
    if not sim_success:
        print("示例模拟失败，退出。")
        return

    # 2. 可视化流体场
    print("\n2. 可视化流体场...")
    planner.visualize_fluid_fields()
    plt.show(block=False) # Show non-blocking

    # 3. 使用不同方法寻找路径
    print("\n3. 规划路径...")
    planner.find_path_streamline(max_steps=2000) # Shorter steps for example
    planner.find_path_fluid_astar(fluid_weight=0.6)

    # 4. 比较路径并可视化
    print("\n4. 比较路径规划方法...")
    planner.compare_paths_and_visualize()
    plt.show() # Show all plots

    print("\n=== 示例完成 (Example Completed) ===")
    return planner


def main():
    """
    主函数：控制程序执行流程。
    用户可在此处修改参数以自定义程序行为。

    主要参数:
    - use_excel_map: 是否从Excel文件加载地图
    - excel_map_file: Excel文件路径
    - run_full_example: 是否运行集成示例

    如果run_full_example为False，则可以自定义运行流程，
    包括选择地图来源、设置模拟参数、路径规划参数等。

    这个函数是程序的入口点，通过修改这里的参数，
    用户可以灵活地控制整个系统的行为。
    """
    print("=== 流体动力学路径规划系统启动 ===")
    # 参数设置
    use_excel_map = False  # True: 从Excel加载地图; False: 使用内置示例地图
    excel_map_file = 'label/sea_ice_419.xlsx' # 仅当 use_excel_map 为 True 时使用

    run_full_example = True # True: 运行集成的示例; False: 自定义运行流程

    if run_full_example:
        example_usage()
    else:
        # 自定义运行流程 (用户可在此配置)
        planner = FluidPathPlanner()

        if use_excel_map:
            print(f"\n1. 从Excel加载地图: {excel_map_file}")
            if not planner.load_map_from_excel(excel_map_file):
                print("错误: 无法加载Excel地图。")
                return
        else:
            print("\n1. 使用默认程序生成地图")
            default_grid_size = 50
            default_map = np.zeros((default_grid_size, default_grid_size), dtype=int)
            default_map[10:40, 25] = 1  # 垂直墙
            default_map[20, 10:40] = 1  # 水平墙
            planner = FluidPathPlanner(grid_map_input=default_map) # Re-init with map

        # 2. 运行流体模拟
        print("\n2. 运行流体模拟...")
        planner.run_fluid_simulation(
            max_iter=3000, tau=0.7, inlet_velocity=0.05,
            convergence_threshold=1e-6
        )

        # 3. 可视化流体场
        print("\n3. 可视化流体场...")
        planner.visualize_fluid_fields()
        plt.show(block=False)

        # 4. 路径规划
        print("\n4. 执行路径规划...")
        planner.find_path_streamline()
        planner.find_path_fluid_astar(fluid_weight=0.7, diagonal_movement=True)

        # 5. 比较和可视化路径
        print("\n5. 比较路径规划方法...")
        planner.compare_paths_and_visualize()
        plt.show()

    print("\n=== 系统演示完成 ===")


if __name__ == "__main__":
    main()